# 📊 Guide Frontend - Tableau de Bord Admin (Version Simplifiée)

## 🎯 Vue d'Ensemble

Implémentation du tableau de bord BI pour les admins avec 3 analyses principales :
1. **Gestion d'équipes** (cartes métriques)
2. **Distribution des événements** (pie chart)
3. **Distribution des tâches** (pie chart)

## 🌐 API Endpoints

### Endpoint Principal
```
GET /api/bi/admin/dashboard/
```

**Paramètres :**
- `period` : `today` | `1h` | `24h` | `7d` | `30d` (défaut: `today`)
- `manual_refresh` : `true` | `false` (défaut: `true`)

**Headers requis :**
```json
{
    "Authorization": "Bearer YOUR_ADMIN_TOKEN",
    "Content-Type": "application/json"
}
```

**Exemples d'URLs :**
```
GET /api/bi/admin/dashboard/                    # Aujourd'hui
GET /api/bi/admin/dashboard/?period=1h         # Dernière heure
GET /api/bi/admin/dashboard/?period=7d         # 7 jours
```

## 📋 Structure de Réponse

```json
{
    "admin_name": "Admin Name",
    "is_team_leader": true,
    
    "metric_cards": [
        {
            "title": "Équipes gérées",
            "value": 3,
            "color": "#3B82F6",
            "icon": "users"
        },
        {
            "title": "Membres d'équipe", 
            "value": 12,
            "color": "#10B981",
            "icon": "user-group"
        },
        {
            "title": "Progression moyenne",
            "value": "67.5%",
            "color": "#8B5CF6",
            "icon": "trending-up"
        }
    ],
    
    "charts": {
        "events_distribution": {
            "title": "Distribution des Événements d'Équipe",
            "data": [
                {"name": "À faire", "value": 8, "color": "#3B82F6", "percentage": 40.0},
                {"name": "En cours", "value": 7, "color": "#F59E0B", "percentage": 35.0},
                {"name": "Terminés", "value": 5, "color": "#10B981", "percentage": 25.0}
            ]
        },
        "tasks_distribution": {
            "title": "Distribution des Tâches d'Équipe",
            "data": [
                {"name": "À faire", "value": 15, "color": "#3B82F6", "percentage": 50.0},
                {"name": "En cours", "value": 10, "color": "#F59E0B", "percentage": 33.3},
                {"name": "Terminées", "value": 5, "color": "#10B981", "percentage": 16.7}
            ]
        }
    },
    
    "metadata": {
        "current_period": {"period": "today", "period_name": "Aujourd'hui"},
        "available_periods": [
            {"value": "today", "label": "Aujourd'hui"},
            {"value": "1h", "label": "Dernière heure"},
            {"value": "24h", "label": "Dernières 24h"},
            {"value": "7d", "label": "Derniers 7 jours"},
            {"value": "30d", "label": "Derniers 30 jours"}
        ]
    }
}
```

## 🔧 Service API

```javascript
// services/adminDashboard.js
class AdminDashboardService {
    constructor() {
        this.baseURL = 'http://localhost:8000/api';
        this.token = localStorage.getItem('authToken');
    }

    async getDashboard(period = 'today') {
        const params = new URLSearchParams({ period, manual_refresh: 'true' });
        
        const response = await fetch(`${this.baseURL}/bi/admin/dashboard/?${params}`, {
            headers: {
                'Authorization': `Bearer ${this.token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) throw new Error(`Erreur ${response.status}`);
        return response.json();
    }
}

export default new AdminDashboardService();
```

## 🎨 Composants React

### 1. Hook Principal

```javascript
// hooks/useAdminDashboard.js
import { useState, useEffect } from 'react';
import adminDashboard from '../services/adminDashboard';

export const useAdminDashboard = () => {
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [period, setPeriod] = useState('today');

    const fetchData = async (newPeriod = period) => {
        setLoading(true);
        setError(null);
        try {
            const result = await adminDashboard.getDashboard(newPeriod);
            setData(result);
            setPeriod(newPeriod);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => { fetchData(); }, []);

    return { data, loading, error, period, fetchData, setPeriod };
};
```

### 2. Composant Principal

```javascript
// components/AdminDashboard.jsx
import React from 'react';
import { useAdminDashboard } from '../hooks/useAdminDashboard';

const AdminDashboard = () => {
    const { data, loading, error, period, fetchData } = useAdminDashboard();

    if (loading) return <div className="text-center">Chargement...</div>;
    if (error) return <div className="text-red-500">Erreur: {error}</div>;
    if (!data) return <div>Aucune donnée</div>;

    return (
        <div className="space-y-6">
            {/* En-tête avec filtres */}
            <div className="flex justify-between items-center">
                <h1 className="text-2xl font-bold">Tableau de Bord Admin</h1>
                <div className="flex space-x-2">
                    {data.metadata.available_periods.map(p => (
                        <button
                            key={p.value}
                            onClick={() => fetchData(p.value)}
                            className={`px-3 py-1 rounded ${
                                period === p.value ? 'bg-blue-500 text-white' : 'bg-gray-200'
                            }`}
                        >
                            {p.label}
                        </button>
                    ))}
                    <button 
                        onClick={() => fetchData(period)}
                        className="px-3 py-1 bg-green-500 text-white rounded"
                    >
                        🔄 Actualiser
                    </button>
                </div>
            </div>

            {/* Cartes de métriques */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {data.metric_cards.map((card, i) => (
                    <div key={i} className="bg-white p-4 rounded-lg shadow border-l-4" 
                         style={{borderLeftColor: card.color}}>
                        <h3 className="text-sm text-gray-600">{card.title}</h3>
                        <p className="text-2xl font-bold">{card.value}</p>
                    </div>
                ))}
            </div>

            {/* Graphiques */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <ChartCard chart={data.charts.events_distribution} />
                <ChartCard chart={data.charts.tasks_distribution} />
            </div>
        </div>
    );
};

export default AdminDashboard;
```

### 3. Composant Graphique

```javascript
// components/ChartCard.jsx
import React from 'react';

const ChartCard = ({ chart }) => {
    const total = chart.data.reduce((sum, item) => sum + item.value, 0);

    return (
        <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4">{chart.title}</h3>
            
            {/* Graphique simple avec barres */}
            <div className="space-y-3">
                {chart.data.map((item, i) => (
                    <div key={i} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                            <div className="w-4 h-4 rounded" style={{backgroundColor: item.color}}></div>
                            <span className="text-sm">{item.name}</span>
                        </div>
                        <div className="text-right">
                            <div className="font-bold">{item.value}</div>
                            <div className="text-xs text-gray-500">{item.percentage}%</div>
                        </div>
                    </div>
                ))}
            </div>

            {/* Barre de progression globale */}
            <div className="mt-4 bg-gray-200 rounded-full h-2">
                {chart.data.map((item, i) => (
                    <div
                        key={i}
                        className="h-2 inline-block"
                        style={{
                            backgroundColor: item.color,
                            width: `${(item.value / total) * 100}%`
                        }}
                    ></div>
                ))}
            </div>
        </div>
    );
};

export default ChartCard;
```

## 🧪 Tests Postman

### Test 1: Dashboard par défaut
```
GET http://localhost:8000/api/bi/admin/dashboard/
Authorization: Bearer YOUR_ADMIN_TOKEN
```

### Test 2: Filtrage par période
```
GET http://localhost:8000/api/bi/admin/dashboard/?period=7d
Authorization: Bearer YOUR_ADMIN_TOKEN
```

### Test 3: Avec rafraîchissement
```
GET http://localhost:8000/api/bi/admin/dashboard/?period=today&manual_refresh=true
Authorization: Bearer YOUR_ADMIN_TOKEN
```

## 🎯 Points Clés

### Fonctionnalités
- ✅ 3 cartes de métriques (équipes, membres, progression)
- ✅ 2 graphiques de distribution (événements, tâches)
- ✅ Filtrage par période (5 options)
- ✅ Bouton de rafraîchissement manuel
- ✅ Responsive design

### Sécurité
- ✅ Token JWT requis
- ✅ Accès admin uniquement
- ✅ Données filtrées par responsabilité

### Performance
- ✅ Calculs en temps réel
- ✅ Mise à jour manuelle
- ✅ Gestion d'erreurs

## 🚀 Intégration

1. **Copier le service** `adminDashboard.js`
2. **Copier le hook** `useAdminDashboard.js`
3. **Copier les composants** `AdminDashboard.jsx` et `ChartCard.jsx`
4. **Ajouter la route** dans votre router
5. **Tester avec Postman** pour valider l'API

**C'est tout ! Le dashboard admin est prêt à fonctionner.** 🎉
