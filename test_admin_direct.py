#!/usr/bin/env python3
"""
Script de test direct pour les fonctions du tableau de bord admin
"""

import os
import sys
import django
from datetime import datetime, timezone

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

django.setup()

from core.mongo_models import User, Team
from core.models.event_model import Event
from core.models.team_task_model import TeamTask
from core.models.bi_model import AdminActivityTracker
from core.views.bi_views import safe_percentage, safe_int, safe_float
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_admin_stats_calculation():
    """
    Teste directement les calculs des statistiques admin
    """
    print("=== Test direct des calculs admin ===\n")
    
    # 1. Trouver un admin
    admin_users = User.objects(role='admin')
    if not admin_users:
        print("❌ Aucun admin trouvé dans la base de données")
        return False
    
    admin = admin_users.first()
    admin_id = str(admin.id)
    print(f"✅ Admin trouvé: {admin.name} ({admin.email})")
    
    # 2. Tester la mise à jour des statistiques
    print("\n--- Test de mise à jour des statistiques ---")
    
    try:
        admin_stats = AdminActivityTracker.update_admin_stats(admin_id, 'today')
        
        if admin_stats:
            print("✅ Mise à jour des statistiques réussie")
            
            # Vérifier les valeurs
            total_teams = safe_int(admin_stats.total_teams)
            total_members = safe_int(admin_stats.total_team_members)
            events_total = safe_int(admin_stats.team_events_total)
            events_pending = safe_int(admin_stats.team_events_pending)
            events_completed = safe_int(admin_stats.team_events_completed)
            tasks_total = safe_int(admin_stats.team_tasks_total)
            tasks_pending = safe_int(admin_stats.team_tasks_pending)
            tasks_completed = safe_int(admin_stats.team_tasks_completed)
            progress = safe_float(admin_stats.team_progress_average)
            
            print(f"Équipes gérées: {total_teams}")
            print(f"Membres total: {total_members}")
            print(f"Événements total: {events_total}")
            print(f"Événements en attente: {events_pending}")
            print(f"Événements terminés: {events_completed}")
            print(f"Tâches total: {tasks_total}")
            print(f"Tâches en attente: {tasks_pending}")
            print(f"Tâches terminées: {tasks_completed}")
            print(f"Progression moyenne: {progress}%")
            
            # Tester les calculs de pourcentage
            events_completion_rate = safe_percentage(events_completed, events_total)
            tasks_completion_rate = safe_percentage(tasks_completed, tasks_total)
            
            print(f"Taux de completion événements: {events_completion_rate}%")
            print(f"Taux de completion tâches: {tasks_completion_rate}%")
            
            # Vérifier qu'aucune valeur n'est NaN
            values_to_check = [
                total_teams, total_members, events_total, events_pending, 
                events_completed, tasks_total, tasks_pending, tasks_completed,
                progress, events_completion_rate, tasks_completion_rate
            ]
            
            nan_found = False
            for i, value in enumerate(values_to_check):
                if isinstance(value, float) and (str(value) == 'nan' or str(value) == 'inf'):
                    print(f"❌ Valeur NaN détectée à l'index {i}: {value}")
                    nan_found = True
            
            if not nan_found:
                print("✅ Aucune valeur NaN détectée dans les calculs")
            
            return not nan_found
            
        else:
            print("❌ Échec de la mise à jour des statistiques")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_chart_data_generation():
    """
    Teste la génération des données de graphiques
    """
    print("\n=== Test de génération des données de graphiques ===\n")
    
    # Simuler des données d'admin stats
    class MockAdminStats:
        def __init__(self):
            self.team_events_total = 10
            self.team_events_pending = 3
            self.team_events_completed = 5
            self.team_events_archived = 2
            self.team_tasks_total = 15
            self.team_tasks_pending = 4
            self.team_tasks_in_progress = 6
            self.team_tasks_in_revision = 2
            self.team_tasks_completed = 2
            self.team_tasks_archived = 1
    
    mock_stats = MockAdminStats()
    
    # Test du graphique des événements
    print("--- Test graphique événements ---")
    
    events_pending = safe_int(mock_stats.team_events_pending)
    events_completed = safe_int(mock_stats.team_events_completed)
    events_archived = safe_int(mock_stats.team_events_archived)
    events_total = safe_int(mock_stats.team_events_total)
    
    events_chart_data = [
        {
            'name': 'En attente',
            'value': events_pending,
            'percentage': safe_percentage(events_pending, events_total)
        },
        {
            'name': 'Terminés',
            'value': events_completed,
            'percentage': safe_percentage(events_completed, events_total)
        },
        {
            'name': 'Archivés',
            'value': events_archived,
            'percentage': safe_percentage(events_archived, events_total)
        }
    ]
    
    print(f"Total événements: {events_total}")
    for point in events_chart_data:
        name = point['name']
        value = point['value']
        percentage = point['percentage']
        print(f"  - {name}: {value} ({percentage}%)")
        
        # Vérifier qu'il n'y a pas de NaN
        if isinstance(percentage, float) and (str(percentage) == 'nan' or str(percentage) == 'inf'):
            print(f"❌ NaN détecté dans {name}")
            return False
    
    # Test du graphique des tâches
    print("\n--- Test graphique tâches ---")
    
    tasks_pending = safe_int(mock_stats.team_tasks_pending)
    tasks_in_progress = safe_int(mock_stats.team_tasks_in_progress)
    tasks_in_revision = safe_int(mock_stats.team_tasks_in_revision)
    tasks_completed = safe_int(mock_stats.team_tasks_completed)
    tasks_archived = safe_int(mock_stats.team_tasks_archived)
    tasks_total = safe_int(mock_stats.team_tasks_total)
    
    tasks_chart_data = [
        {
            'name': 'À faire',
            'value': tasks_pending,
            'percentage': safe_percentage(tasks_pending, tasks_total)
        },
        {
            'name': 'En cours',
            'value': tasks_in_progress,
            'percentage': safe_percentage(tasks_in_progress, tasks_total)
        },
        {
            'name': 'En révision',
            'value': tasks_in_revision,
            'percentage': safe_percentage(tasks_in_revision, tasks_total)
        },
        {
            'name': 'Terminées',
            'value': tasks_completed,
            'percentage': safe_percentage(tasks_completed, tasks_total)
        },
        {
            'name': 'Archivées',
            'value': tasks_archived,
            'percentage': safe_percentage(tasks_archived, tasks_total)
        }
    ]
    
    print(f"Total tâches: {tasks_total}")
    for point in tasks_chart_data:
        name = point['name']
        value = point['value']
        percentage = point['percentage']
        print(f"  - {name}: {value} ({percentage}%)")
        
        # Vérifier qu'il n'y a pas de NaN
        if isinstance(percentage, float) and (str(percentage) == 'nan' or str(percentage) == 'inf'):
            print(f"❌ NaN détecté dans {name}")
            return False
    
    # Vérifier que les totaux correspondent
    events_sum = events_pending + events_completed + events_archived
    tasks_sum = tasks_pending + tasks_in_progress + tasks_in_revision + tasks_completed + tasks_archived
    
    print(f"\n--- Vérification des totaux ---")
    print(f"Événements - Total: {events_total}, Somme: {events_sum}, Match: {events_total == events_sum}")
    print(f"Tâches - Total: {tasks_total}, Somme: {tasks_sum}, Match: {tasks_total == tasks_sum}")
    
    print("✅ Génération des données de graphiques réussie")
    return True

if __name__ == "__main__":
    success1 = test_admin_stats_calculation()
    success2 = test_chart_data_generation()
    
    if success1 and success2:
        print("\n🎉 Tous les tests directs sont réussis!")
        print("🎉 Les corrections du tableau de bord admin fonctionnent correctement!")
    else:
        print("\n❌ Des problèmes ont été détectés dans les calculs admin.")
        sys.exit(1)
