from mongoengine import Document, StringField, DateTimeField, DictField, ListField, IntField, FloatField
from datetime import datetime, timezone
from bson import ObjectId

class BiMetricSnapshot(Document):
    """
    Modèle pour stocker les instantanés de métriques BI
    Nouveau modèle pour éviter les conflits avec l'ancien
    """
    id = StringField(primary_key=True, default=lambda: str(ObjectId()))
    dashboard_type = StringField(required=True, choices=[
        'super_admin',  # Tableau de bord pour super admin
        'admin',        # Tableau de bord pour admin
        'employee',     # Tableau de bord pour employé
        'client'        # Tableau de bord pour client
    ])
    user_id = StringField(required=False, default="")  # ID de l'utilisateur concerné (pour les métriques personnelles)
    team_id = StringField(required=False, default="")  # ID de l'équipe concernée (pour les métriques d'équipe)
    metrics = DictField(required=True)  # Données complètes des métriques (format JSON)
    timestamp = DateTimeField(default=datetime.now(timezone.utc))  # Horodatage de la capture des métriques
    metric_type = StringField(required=False, default="daily")  # Type de métrique: daily, weekly, monthly, real_time
    period_start = DateTimeField(required=False)  # Début de la période pour les métriques agrégées
    period_end = DateTimeField(required=False)  # Fin de la période pour les métriques agrégées
    created_at = DateTimeField(default=datetime.now(timezone.utc))
    updated_at = DateTimeField(default=datetime.now(timezone.utc))

    meta = {
        'collection': 'bi_metrics_snapshots',  # Nouvelle collection pour éviter les conflits
        'indexes': [
            {'fields': ['dashboard_type']},
            {'fields': ['user_id']},
            {'fields': ['team_id']},
            {'fields': ['timestamp']},
            {'fields': ['created_at']},
            {'fields': ['metric_type']},
            {'fields': ['period_start', 'period_end']}
        ]
    }

    def save(self, *args, **kwargs):
        # Mise à jour de updated_at lors des modifications
        self.updated_at = datetime.now(timezone.utc)
        return super(BiMetricSnapshot, self).save(*args, **kwargs)

# Garder l'ancien modèle pour la compatibilité, mais sans index problématique
class BiMetric(Document):
    """
    Modèle pour stocker les métriques BI (ancien modèle gardé pour compatibilité)
    """
    id = StringField(primary_key=True, default=lambda: str(ObjectId()))
    metric_type = StringField(required=True, choices=[
        'user_activity',         # Activité des utilisateurs (pour super admin)
        'team_task_status',      # Statut des tâches d'équipe (pour admin)
        'team_event_status',     # Statut des événements d'équipe (pour admin)
        'personal_task_status',  # Statut des tâches personnelles (pour employé/client)
        'personal_event_status', # Statut des événements personnels (pour employé/client)
        'pomodoro_usage'         # Utilisation du mode Pomodoro (pour tous)
    ])
    user_id = StringField(required=False, default=None)  # ID de l'utilisateur concerné (pour les métriques personnelles)
    team_id = StringField(required=False, default=None)  # ID de l'équipe concernée (pour les métriques d'équipe)
    data = DictField(required=True)  # Données de la métrique (format JSON)
    created_at = DateTimeField(default=datetime.now(timezone.utc))
    updated_at = DateTimeField(default=datetime.now(timezone.utc))

    meta = {
        'collection': 'bi_metrics',
        'indexes': []  # Pas d'index pour éviter les erreurs
    }

    def save(self, *args, **kwargs):
        # Mise à jour de updated_at lors des modifications
        self.updated_at = datetime.now(timezone.utc)
        return super(BiMetric, self).save(*args, **kwargs)

class BiDashboard(Document):
    """
    Modèle pour stocker les configurations de tableau de bord BI
    """
    id = StringField(primary_key=True, default=lambda: str(ObjectId()))
    user_id = StringField(required=True, default=None)  # ID de l'utilisateur propriétaire du tableau de bord
    dashboard_type = StringField(required=True, choices=[
        'super_admin',  # Tableau de bord pour super admin
        'admin',        # Tableau de bord pour admin
        'employee',     # Tableau de bord pour employé
        'client'        # Tableau de bord pour client
    ])
    title = StringField(required=True)
    layout = DictField(required=True)  # Configuration de la mise en page (format JSON)
    metrics = DictField(required=True)  # Liste des métriques à afficher (format JSON)
    created_at = DateTimeField(default=datetime.now(timezone.utc))
    updated_at = DateTimeField(default=datetime.now(timezone.utc))

    meta = {
        'collection': 'bi_dashboards',
        'indexes': [
            {'fields': ['user_id']},
            {'fields': ['dashboard_type']}
        ]
    }

    def save(self, *args, **kwargs):
        # Mise à jour de updated_at lors des modifications
        self.updated_at = datetime.now(timezone.utc)
        return super(BiDashboard, self).save(*args, **kwargs)


class DailyLoginTracker(Document):
    """
    Modèle pour tracker les connexions quotidiennes des utilisateurs
    """
    id = StringField(primary_key=True, default=lambda: str(ObjectId()))
    date = DateTimeField(required=True)  # Date du jour (format YYYY-MM-DD 00:00:00)
    users_logged_today = ListField(StringField())  # Liste des IDs des utilisateurs connectés aujourd'hui
    total_logins_today = IntField(default=0)  # Nombre total de connexions aujourd'hui
    unique_users_today = IntField(default=0)  # Nombre d'utilisateurs uniques connectés aujourd'hui
    created_at = DateTimeField(default=datetime.now(timezone.utc))
    updated_at = DateTimeField(default=datetime.now(timezone.utc))

    meta = {
        'collection': 'daily_login_tracker',
        'indexes': [
            {'fields': ['date'], 'unique': True},  # Une seule entrée par jour
            {'fields': ['created_at']}
        ]
    }

    def save(self, *args, **kwargs):
        # Mise à jour de updated_at lors des modifications
        self.updated_at = datetime.now(timezone.utc)
        # Mettre à jour le nombre d'utilisateurs uniques
        self.unique_users_today = len(set(self.users_logged_today)) if self.users_logged_today else 0
        return super(DailyLoginTracker, self).save(*args, **kwargs)

    @classmethod
    def add_user_login(cls, user_id):
        """
        Ajoute une connexion utilisateur pour aujourd'hui
        """
        from datetime import datetime, timezone

        # Obtenir la date d'aujourd'hui (début de journée)
        today = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)

        # Récupérer ou créer l'entrée pour aujourd'hui
        try:
            tracker = cls.objects.get(date=today)
        except cls.DoesNotExist:
            tracker = cls(
                date=today,
                users_logged_today=[],
                total_logins_today=0,
                unique_users_today=0
            )

        # Ajouter l'utilisateur s'il n'est pas déjà dans la liste
        if user_id not in tracker.users_logged_today:
            tracker.users_logged_today.append(user_id)

        # Incrémenter le nombre total de connexions
        tracker.total_logins_today += 1

        # Sauvegarder
        tracker.save()

        return tracker

    @classmethod
    def get_users_logged_today(cls):
        """
        Retourne le nombre d'utilisateurs connectés aujourd'hui
        """
        from datetime import datetime, timezone

        today = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)

        try:
            tracker = cls.objects.get(date=today)
            return tracker.unique_users_today
        except cls.DoesNotExist:
            return 0

    @classmethod
    def get_total_logins_today(cls):
        """
        Retourne le nombre total de connexions aujourd'hui
        """
        from datetime import datetime, timezone

        today = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)

        try:
            tracker = cls.objects.get(date=today)
            return tracker.total_logins_today
        except cls.DoesNotExist:
            return 0


class AdminActivityTracker(Document):
    """
    Modèle pour tracker les activités des admins en temps réel
    """
    id = StringField(primary_key=True, default=lambda: str(ObjectId()))
    admin_id = StringField(required=True)  # ID de l'admin
    admin_name = StringField(required=True)  # Nom de l'admin
    date = DateTimeField(required=True)  # Date du jour (format YYYY-MM-DD 00:00:00)

    # Statistiques des équipes
    teams_managed = ListField(StringField())  # Liste des IDs des équipes gérées
    total_teams = IntField(default=0)  # Nombre total d'équipes gérées
    total_team_members = IntField(default=0)  # Nombre total de membres dans toutes les équipes

    # Statistiques des événements d'équipe
    team_events_created = IntField(default=0)  # Événements créés aujourd'hui
    team_events_completed = IntField(default=0)  # Événements terminés aujourd'hui
    team_events_pending = IntField(default=0)  # Événements en attente
    team_events_total = IntField(default=0)  # Total des événements gérés

    # Statistiques des tâches d'équipe
    team_tasks_created = IntField(default=0)  # Tâches créées aujourd'hui
    team_tasks_completed = IntField(default=0)  # Tâches terminées aujourd'hui
    team_tasks_pending = IntField(default=0)  # Tâches en attente
    team_tasks_total = IntField(default=0)  # Total des tâches gérées

    # Progression des équipes
    team_progress_average = FloatField(default=0.0)  # Progression moyenne des équipes (%)
    most_active_team_id = StringField()  # ID de l'équipe la plus active
    most_active_team_name = StringField()  # Nom de l'équipe la plus active

    created_at = DateTimeField(default=datetime.now(timezone.utc))
    updated_at = DateTimeField(default=datetime.now(timezone.utc))

    meta = {
        'collection': 'admin_activity_tracker',
        'indexes': [
            {'fields': ['admin_id', 'date'], 'unique': True},  # Une seule entrée par admin par jour
            {'fields': ['date']},
            {'fields': ['admin_id']}
        ]
    }

    def save(self, *args, **kwargs):
        self.updated_at = datetime.now(timezone.utc)
        return super(AdminActivityTracker, self).save(*args, **kwargs)

    @classmethod
    def update_admin_stats(cls, admin_id, period='today'):
        """
        Met à jour les statistiques d'un admin pour une période donnée
        """
        from ..mongo_models import User, Team
        from ..models.event_model import Event
        from ..models.team_task_model import TeamTask
        from datetime import datetime, timezone, timedelta
        import logging

        logger = logging.getLogger(__name__)

        # Obtenir la date selon la période
        now = datetime.now(timezone.utc)
        if period == 'today':
            period_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        elif period == '1h':
            period_start = now - timedelta(hours=1)
        elif period == '24h':
            period_start = now - timedelta(days=1)
        elif period == '7d':
            period_start = now - timedelta(days=7)
        elif period == '30d':
            period_start = now - timedelta(days=30)
        else:
            period_start = now.replace(hour=0, minute=0, second=0, microsecond=0)

        try:
            # Récupérer l'admin
            admin = User.objects.get(id=admin_id, role='admin')

            # Récupérer les équipes gérées par cet admin
            admin_teams = Team.objects(responsable=admin_id)
            team_ids = [str(team.id) for team in admin_teams]

            # Calculer les statistiques des équipes
            total_teams = len(admin_teams)
            total_team_members = sum(len(team.members) for team in admin_teams)

            # Calculer les statistiques des événements
            team_events_total = Event.objects(team_id__in=team_ids).count()
            team_events_created = Event.objects(
                team_id__in=team_ids,
                created_at__gte=period_start
            ).count()
            team_events_completed = Event.objects(
                team_id__in=team_ids,
                status='completed',
                updated_at__gte=period_start
            ).count()
            team_events_pending = Event.objects(
                team_id__in=team_ids,
                status='pending'
            ).count()

            # Calculer les statistiques des tâches
            team_tasks_total = TeamTask.objects(team_id__in=team_ids).count()
            team_tasks_created = TeamTask.objects(
                team_id__in=team_ids,
                created_at__gte=period_start
            ).count()
            team_tasks_completed = TeamTask.objects(
                team_id__in=team_ids,
                status='completed',
                updated_at__gte=period_start
            ).count()
            team_tasks_pending = TeamTask.objects(
                team_id__in=team_ids,
                status='pending'
            ).count()

            # Calculer la progression moyenne des équipes
            team_progress_scores = []
            most_active_team = None
            max_activity_score = 0

            for team in admin_teams:
                team_id = str(team.id)

                # Calculer le score d'activité de l'équipe
                team_events = Event.objects(team_id=team_id).count()
                team_tasks = TeamTask.objects(team_id=team_id).count()
                team_completed_events = Event.objects(team_id=team_id, status='completed').count()
                team_completed_tasks = TeamTask.objects(team_id=team_id, status='completed').count()

                # Score d'activité = nombre total d'éléments + bonus pour les éléments terminés
                activity_score = team_events + team_tasks + (team_completed_events * 2) + (team_completed_tasks * 2)

                if activity_score > max_activity_score:
                    max_activity_score = activity_score
                    most_active_team = team

                # Calculer le pourcentage de progression
                total_items = team_events + team_tasks
                completed_items = team_completed_events + team_completed_tasks
                progress = (completed_items / total_items * 100) if total_items > 0 else 0
                team_progress_scores.append(progress)

            team_progress_average = sum(team_progress_scores) / len(team_progress_scores) if team_progress_scores else 0

            # Créer ou mettre à jour l'entrée du tracker
            if period == 'today':
                try:
                    tracker = cls.objects.get(admin_id=admin_id, date=period_start)
                except cls.DoesNotExist:
                    tracker = cls(
                        admin_id=admin_id,
                        admin_name=admin.name,
                        date=period_start
                    )
            else:
                # Pour les autres périodes, créer une entrée temporaire
                tracker = cls(
                    admin_id=admin_id,
                    admin_name=admin.name,
                    date=period_start
                )

            # Mettre à jour les statistiques
            tracker.teams_managed = team_ids
            tracker.total_teams = total_teams
            tracker.total_team_members = total_team_members
            tracker.team_events_created = team_events_created
            tracker.team_events_completed = team_events_completed
            tracker.team_events_pending = team_events_pending
            tracker.team_events_total = team_events_total
            tracker.team_tasks_created = team_tasks_created
            tracker.team_tasks_completed = team_tasks_completed
            tracker.team_tasks_pending = team_tasks_pending
            tracker.team_tasks_total = team_tasks_total
            tracker.team_progress_average = round(team_progress_average, 2)

            if most_active_team:
                tracker.most_active_team_id = str(most_active_team.id)
                tracker.most_active_team_name = most_active_team.name

            # Sauvegarder seulement pour 'today'
            if period == 'today':
                tracker.save()

            return tracker

        except Exception as e:
            logger.error(f"Erreur lors de la mise à jour des statistiques admin {admin_id}: {str(e)}")
            return None

    @classmethod
    def get_admin_stats(cls, admin_id, period='today'):
        """
        Récupère les statistiques d'un admin pour une période donnée
        """
        if period == 'today':
            today = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
            try:
                return cls.objects.get(admin_id=admin_id, date=today)
            except cls.DoesNotExist:
                # Créer les statistiques si elles n'existent pas
                return cls.update_admin_stats(admin_id, period)
        else:
            # Pour les autres périodes, calculer à la volée
            return cls.update_admin_stats(admin_id, period)